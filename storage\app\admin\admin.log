[2025-08-19 02:14:35] local.ERROR: Attempt to read property "email" on null  
[2025-08-19 02:14:35] local.INFO: number of attempts: 1  
[2025-08-19 02:14:35] local.ERROR: Attempt to read property "email" on null  
[2025-08-19 02:14:35] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Attempt to read property \"email\" on null","code":0}  
[2025-08-19 02:15:25] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-19 02:15:31] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-19 02:16:01] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-19 02:32:50] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-19 02:40:57] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-19 02:54:31] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-19 05:19:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-08-19 05:19:28] local.INFO: user login from 127.0.0.1  
[2025-08-19 06:18:27] local.ERROR: {"query":{"statusType":"APPROVED"},"parameter":{"statusType":"APPROVED"},"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-19 09:23:05] local.ERROR: {"query":{"statusType":"APPROVED"},"parameter":{"statusType":"APPROVED"},"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-08-19 09:23:19] local.INFO: user login from 127.0.0.1  
[2025-08-20 01:28:59] local.INFO: user login from 127.0.0.1  
[2025-08-20 01:32:00] local.INFO: DomainRedemptionScheduler: Skipped by configuration.  
[2025-08-20 01:34:00] local.INFO: DomainRedemptionScheduler: Running...  
[2025-08-20 01:34:01] local.INFO: DomainRedemptionScheduler: Done  
[2025-08-20 01:35:27] local.ERROR: HTTP request returned status code 400:
{"message":"transfer.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"transfer.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 01:35:27] local.ERROR: Undefined array key "data"  
[2025-08-20 01:35:27] local.INFO: number of attempts: 1  
[2025-08-20 01:35:27] local.ERROR: Undefined array key "data"  
[2025-08-20 01:35:27] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 01:38:36] local.ERROR: HTTP request returned status code 400:
{"message":"aurorabuzz.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"aurorabuzz.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 01:38:36] local.ERROR: Undefined array key "data"  
[2025-08-20 01:38:36] local.INFO: number of attempts: 1  
[2025-08-20 01:38:36] local.ERROR: Undefined array key "data"  
[2025-08-20 01:38:36] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 01:38:39] local.ERROR: HTTP request returned status code 400:
{"message":"aurorabuzz.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"aurorabuzz.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 01:38:39] local.ERROR: Undefined array key "data"  
[2025-08-20 01:38:39] local.INFO: number of attempts: 1  
[2025-08-20 01:38:39] local.ERROR: Undefined array key "data"  
[2025-08-20 01:38:39] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 01:42:07] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-20 01:48:09] local.ERROR: HTTP request returned status code 400:
{"message":"radiantrover.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"radiantrover.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 01:48:09] local.ERROR: Undefined array key "data"  
[2025-08-20 01:48:09] local.INFO: number of attempts: 1  
[2025-08-20 01:48:09] local.ERROR: Undefined array key "data"  
[2025-08-20 01:48:09] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 01:49:53] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-20 05:21:37] local.INFO: user login from 127.0.0.1  
[2025-08-20 05:24:04] local.ERROR: HTTP request returned status code 400:
{"message":"jcdonutsph.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"jcdonutsph.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:04] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:04] local.INFO: number of attempts: 1  
[2025-08-20 05:24:04] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:09] local.ERROR: HTTP request returned status code 400:
{"message":"sumasamasila.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"sumasamasila.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:09] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:09] local.INFO: number of attempts: 1  
[2025-08-20 05:24:09] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:09] local.ERROR: HTTP request returned status code 400:
{"message":"midnightvortex.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"midnightvortex.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:09] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:09] local.INFO: number of attempts: 1  
[2025-08-20 05:24:09] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:10] local.ERROR: HTTP request returned status code 400:
{"message":"pwdforyou.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"pwdforyou.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:10] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:10] local.INFO: number of attempts: 1  
[2025-08-20 05:24:10] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:18] local.ERROR: HTTP request returned status code 400:
{"message":"cdforfree.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cdforfree.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:18] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:18] local.INFO: number of attempts: 1  
[2025-08-20 05:24:18] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:24] local.ERROR: HTTP request returned status code 400:
{"message":"redmonday.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"redmonday.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:24] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:24] local.INFO: number of attempts: 1  
[2025-08-20 05:24:24] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:34] local.ERROR: HTTP request returned status code 400:
{"message":"jcdonutsph.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"jcdonutsph.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:34] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:34] local.INFO: number of attempts: 2  
[2025-08-20 05:24:34] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:35] local.ERROR: HTTP request returned status code 400:
{"message":"sumasamasila.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"sumasamasila.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:35] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:35] local.INFO: number of attempts: 2  
[2025-08-20 05:24:35] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:36] local.ERROR: HTTP request returned status code 400:
{"message":"midnightvortex.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"midnightvortex.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:36] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:36] local.INFO: number of attempts: 2  
[2025-08-20 05:24:36] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:36] local.ERROR: HTTP request returned status code 400:
{"message":"pwdforyou.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"pwdforyou.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:36] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:36] local.INFO: number of attempts: 2  
[2025-08-20 05:24:36] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:39] local.ERROR: HTTP request returned status code 400:
{"message":"cdforfree.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cdforfree.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:39] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:39] local.INFO: number of attempts: 2  
[2025-08-20 05:24:39] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:24:40] local.ERROR: HTTP request returned status code 400:
{"message":"redmonday.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"redmonday.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-20 05:24:40] local.ERROR: Undefined array key "data"  
[2025-08-20 05:24:40] local.INFO: number of attempts: 2  
[2025-08-20 05:24:40] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-08-20 05:56:04] local.ERROR: {"query":{"statusType":"DELETED"},"parameter":{"statusType":"DELETED"},"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-20 08:06:49] local.INFO: user login from 127.0.0.1  
[2025-08-22 01:53:28] local.INFO: user login from 127.0.0.1  
[2025-08-22 06:59:17] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-08-22 06:59:29] local.INFO: user login from 127.0.0.1  
[2025-08-26 00:57:51] local.INFO: user login from 127.0.0.1  
[2025-08-26 01:29:43] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-26 01:29:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-26 01:29:57] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-26 01:32:07] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-26 01:34:20] local.INFO: AutoApproveExpiredRequests: Running...  
[2025-08-26 01:34:42] local.ERROR: AutoApproveExpiredRequests: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-08-26 01:34:42] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"AutoApproveExpiredRequests: Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":0}  
[2025-08-26 01:50:24] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:24] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-26 01:50:24] local.INFO: number of attempts: 1  
[2025-08-26 01:50:24] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-26 01:50:26] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:26] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-26 01:50:26] local.INFO: number of attempts: 1  
[2025-08-26 01:50:26] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-26 01:50:36] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:36] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-26 01:50:36] local.INFO: number of attempts: 2  
[2025-08-26 01:50:37] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-26 01:50:38] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:38] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-26 01:50:38] local.INFO: number of attempts: 2  
[2025-08-26 01:50:38] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-26 01:50:41] local.INFO: Domain limittestme.org has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:41] local.ERROR: Domain limittestme.org has clientDeleteProhibited status  
[2025-08-26 01:50:41] local.INFO: number of attempts: 1  
[2025-08-26 01:50:41] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.org has clientDeleteProhibited status","code":0}  
[2025-08-26 01:50:48] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:48] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-26 01:50:48] local.INFO: number of attempts: 3  
[2025-08-26 01:50:48] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-26 01:50:48] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-26 01:50:50] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:50] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-26 01:50:50] local.INFO: number of attempts: 3  
[2025-08-26 01:50:50] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-26 01:50:50] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-26 01:50:51] local.INFO: Domain limittestme.org has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:50:51] local.ERROR: Domain limittestme.org has clientDeleteProhibited status  
[2025-08-26 01:50:51] local.INFO: number of attempts: 2  
[2025-08-26 01:50:51] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.org has clientDeleteProhibited status","code":0}  
[2025-08-26 01:51:37] local.INFO: log out  
[2025-08-26 01:51:41] local.INFO: user login from 127.0.0.1  
[2025-08-26 01:53:01] local.INFO: AutoApproveExpiredRequests: Running...  
[2025-08-26 01:53:11] local.INFO: AutoApproveExpiredRequests: Processed 3 expired requests  
[2025-08-26 01:53:11] local.INFO: AutoApproveExpiredRequests: Done  
[2025-08-26 01:53:37] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:53:37] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-26 01:53:37] local.INFO: number of attempts: 1  
[2025-08-26 01:53:37] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-26 01:53:39] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:53:39] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-26 01:53:39] local.INFO: number of attempts: 1  
[2025-08-26 01:53:39] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-26 01:53:41] local.INFO: Domain limittestme.org has clientDeleteProhibited status, cannot delete  
[2025-08-26 01:53:41] local.ERROR: Domain limittestme.org has clientDeleteProhibited status  
[2025-08-26 01:53:41] local.INFO: number of attempts: 3  
[2025-08-26 01:53:41] local.ERROR: Domain limittestme.org has clientDeleteProhibited status  
[2025-08-26 01:53:41] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.org has clientDeleteProhibited status","code":0}  
[2025-08-26 02:06:15] local.INFO: AutoApproveExpiredRequests: Running...  
[2025-08-26 02:06:23] local.INFO: AutoApproveExpiredRequests: Processed 3 expired requests  
[2025-08-26 02:06:23] local.INFO: AutoApproveExpiredRequests: Done  
[2025-08-26 05:45:45] local.INFO: user login from 127.0.0.1  
[2025-08-26 07:00:56] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-26 07:03:59] local.ERROR: App\Modules\RequestDelete\Jobs\DomainEppCancellation has been attempted too many times.  
[2025-08-26 07:03:59] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\RequestDelete\\Jobs\\DomainEppCancellation has been attempted too many times.","code":0}  
[2025-08-26 07:03:59] local.ERROR: App\Modules\RequestDelete\Jobs\DomainEppCancellation has been attempted too many times.  
[2025-08-26 07:03:59] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\RequestDelete\\Jobs\\DomainEppCancellation has been attempted too many times.","code":0}  
[2025-08-26 07:04:03] local.INFO: Domain limittestme.org has clientDeleteProhibited status, cannot delete  
[2025-08-26 07:04:03] local.ERROR: Domain limittestme.org has clientDeleteProhibited status  
[2025-08-26 07:04:03] local.INFO: number of attempts: 1  
[2025-08-26 07:04:03] local.ERROR: Domain limittestme.org has clientDeleteProhibited status  
[2025-08-26 07:04:03] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.org has clientDeleteProhibited status","code":0}  
[2025-08-26 07:05:31] local.ERROR: {"query":[],"parameter":{"domainId":119,"createdDate":"2025-08-22 08:48:40","support_note":"Domain deletion request rejected."},"error":"ErrorException","message":"Undefined array key \"domainName\"","code":0}  
[2025-08-26 07:09:45] local.ERROR: {"query":[],"parameter":[],"error":"TypeError","message":"App\\Modules\\Client\\Services\\ClientDomainLogService::formatDate(): Argument #1 ($dateString) must be of type string, null given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Modules\\Client\\Services\\ClientDomainLogService.php on line 132","code":0}  
[2025-08-26 07:09:49] local.ERROR: {"query":[],"parameter":[],"error":"TypeError","message":"App\\Modules\\Client\\Services\\ClientDomainLogService::formatDate(): Argument #1 ($dateString) must be of type string, null given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Modules\\Client\\Services\\ClientDomainLogService.php on line 132","code":0}  
